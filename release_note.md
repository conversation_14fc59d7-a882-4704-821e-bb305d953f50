# 升级注意事项
1. *** 0.42 版本增加了群成员数限制，默认为2000。如果您想修改默认值，可以在升级版本之后，修改t_setting表，把默认的大小改为您期望的。另外修改t_group表，把已经存在的群组max_member_count改成您期望的，然后重启。***
2. *** 0.46和0.47版本升级到0.48及以后版本时，可能会提示flyway migrate 38错误，请执行 [修复脚本](https://github.com/wildfirechat/server/blob/wildfirechat/flyway_repaire_migrate_38.sql) 进行修复。0.46和0.47版本之外的版本不会出现此问题。***
3. *** 0.50版本添加了是否允许客户端发送群操作通知的配置。如果您在客户端自定义群通知，需要在服务器端配置允许，没有使用自定义群操作通知的不受影响。***
4. *** 从0.54之前版本升级到0.54及以后版本时，会提示flyway migrate错误。因为0.54版本删除了sql脚本中默认敏感词的内容，flyway checksum失败。请执行```update flyway_schema_history set checksum = 0 where script = 'V17__add_default_sensitive_word.sql';```来修复。***
5. *** 从0.59之前的版本升级到之后的版本执行数据库升级时间比较长，请耐心等待提示运行成功，避免中途中断。 ***
6. *** 0.62/0.63 版本有严重的问题，请使用0.64及以后版本，或者0.61版。 ***
7. *** 从0.68 版本起添加了pc在线是否默认手机接收推送的开关，默认为开，与以前版本作用相反，请注意兼容（可以关掉与之前保持一致或者升级客户端） ***
8. *** 从0.78 版本起把MySQL数据库中关键字都改为大小写敏感，另外生成id的方法也做了改变，只生成小写的id，避免出现id重复的问题，建议所有客户都升级 ***
9. *** 从0.79 版本起把log4j升级到log4j2，因为log4j已经不再维护而且还有已知的漏洞，建议所有客户都升级，升级时注意更新log4j2的配置文件 ***
10. *** 5. 0.97版本更改了启动脚本```wildfirechat.sh```，如果是升级服务，请注意更新启动脚本。***

# 更新记录

--------------
Release note 1.3.6:
1. Server API创建频道时，返回频道ID和频道密钥。
2. 解决外置审核替换内容没有成功的问题。
3. 修改撤回消息限制，可以运行不限制撤回。
4. 创建/更新频道接口可以传菜单参数。
5. Server API支持修改群组类型。

--------------
Release note 1.3.5:
1. 转发消息回调中可以配置带上发送者和目标信息。
2. Server SDK中添加撤回和删除消息。
3. Server SDK中添加发送朋友圈接口。
4. 添加配置是否允许发送消息给被封禁用户。
5. 音视频通话挂断推送中带上发起消息的ID

--------------
Release note 1.3.4:
1. 优化Server SDK中消息内容的处理方式。
2. Server SDK中添加注册消息接口，在使用自定义消息时，不需要修改Server SDK源码。
3. 用户撤回时间配置允许为负数，为负数时可以任意时间撤回。
4. Server SDK添加超时参数。
5. 解决当分段拉取消息时，可能丢失消息的问题。

--------------
Release note 1.3.3:
1. 转移群组时检查新群主是否在群中。
2. 机器人API添加定向消息接口。

--------------
Release note 1.3.2:
1. 机器人Server SDK添加朋友圈相关接口。
2. Server API添加批量获取用户接口。
3. 解决聊天室定向消息无效的问题。
4. 添加配置，可以在禁止私聊时添加例外聊天线路。

--------------
Release note 1.3.1:
1. 透传消息去掉消息ID，减少消息传输大小。
2. 修改添加好友策略，可以设置禁止加自己好友。
3. Server API创建群组时，检查是否设置operator。
4. SDK添加删除广播和组播消息接口

--------------
Release note 1.3.0:
1. 推送信息中添加republish参数，用于标识消息是重新发布的。
2. 添加好友请求限频功能，在IM服务配置中可以配置每天限制添加的好友数。
3. 添加配置，是否允许用户发送消息给被拉黑的用户。
4. 添加鸿蒙回调地址。
5. 创建群组或者添加群组成员时，群组成员的Extra会放到通知消息的extra中，这样便于业务处理通知消息。

--------------
Release note 1.2.9:
1. 解决server api获取群组信息不全的问题。
2. 添加配置使用uuid作为用户群组等ID。

--------------
Release note 1.2.8:
1. server api获取被封禁用户时，过滤掉正常用户。
2. 解决群组标记删除错误。
3. SDK中添加发送消息的示例。
4. 群解散后撤回群中的消息应该失败。
5. 解决某些时候http请求返回信息无法读取问题。

--------------
Release note 1.2.7:
1. 解决搜索频道时某些类型的频道无法被搜索的问题。
2. 添加频道回调新特性，方便使用。
3. 解决机器人API修改机器人用户信息客户端没有更新的问题。
4. Server SDK中关于频道的接口放到ChannelAdmin中。
5. 添加Mesh功能的SDK。

--------------
Release note 1.2.6:
1. 群主强制被退群时（销毁账号/server api强制退群），调整新群主选择，群管理优先，先加入群组优先。
2. 拉取远程消息时，过滤掉可能重复的消息。
3. 退群时添加是否保留消息的参数，可以退群后保留原有消息。
4. 如果不在群组中，当获取群组信息时不更新数据。

--------------
Release note 1.2.5:
1. Server API添加通过邮箱获取用户信息的接口。
2. 支持群组标记删除功能。
3. 添加配置，可以设置在静音时强制推送指定类型消息。
4. 支持用户设置添加好友是否验证功能

--------------
Release note 1.2.4:
1. 去掉用户的默认密码。
2. 去掉频道和机器人的默认头像。
3. 修改在线状态回调数据，返回此用户的所有端状态。

--------------
Release note 1.2.3:
1. 同步专业版IM服务server SDK。
2. 添加对鸿蒙平台的支持。
3. 解决server端音视频SDK信令支持问题。
4. 升级部分依赖，解决漏洞问题

--------------
Release note 1.2.2:
1. 解决搜索特殊字符时的转义问题。
2. SDK添加流式消息。
3. 添加配置，是否禁止拉陌生人入群。
4. 放被拉黑时禁止被拉入群
5. 群组信息变更回调时加上群组信息
6. 机器人sdk添加撤回和更新消息接口

--------------
Release note 1.2.1:
1. 添加获取在线用户功能。
2. 添加获取用户连接session信息的功能。
3. 优化按照用户id搜索用户的逻辑。
4. 解决广播消息没有发给未登录用户的问题。

--------------
Release note 1.2.0:
1. 添加配置开关，可以关掉api/version检查接口。
2. Server SDK会议事件中添加时间戳。
3. 禁止私聊时，如果一方在允许私聊列表中，允许私聊。
4. 解决某些特殊情况下获取网卡信息失败导致启动失败的问题。
5. 支持按照用户ID来搜索用户。
6. 添加获取某个用户是否在聊天室的接口。
7. 优化多端加入聊天室的处理。

--------------
Release note 1.1.9:
1. 解决服务器时间误差太大引起的问题。
2. 添加推送过期配置。
3. 支持发送好友请求后5分钟之内再次发送。
4. Server API支持根据群成员类型获取用户群列表。
5. 添加配置，是否允许群主和群管理员无限制撤回自己的消息。

--------------
Release note 1.1.8:
1. 解决可以把群主设置为管理员的问题。
2. 发送好友请求之后5分钟内可以再次发送。
3. 解决撤回消息中原消息二进制内容格式错误问题。

--------------
Release note 1.1.7:
1. 优化用户搜索功能。
2. 推送消息中使用群备注。
3. 推送消息中添加发送者和目标头像。
4. 优化撤回消息流程。

--------------
Release note 1.1.6:
1. 支持用户设置查找自己的方法。
2. 解决获取用户信息返回type无效的问题。
3. 添加用户获取频道的功能。

--------------
Release note 1.1.5:
1. 添加获取所有用户的Server API。
2. 解决添加好友时没有处理黑名单的问题。

--------------
Release note 1.1.4:
1. 添加PC客户端多端登录互踢时没有更新pc在线状态问题。
2. 添加支持使用AES256加密。
3. Server API添加获取单个群成员的接口。
4. 添加配置，添加机器人为好友时是否自动接受。

--------------
Release note 1.1.3:
1. 添加服务器和客户端时间检查功能。
2. 解决用户被block后session失效的问题。
3. 添加消息撤回回调。
4. 优化群组撤回逻辑。
5. 解决用户离开群组后还能修改群昵称的问题。
6. 修改server api修改好友关系回调数据错误问题。
7. 同步超级群组数据库和数据。

--------------
Release note 1.1.2:
1. 会议SDK添加查询会议是否存在的接口
2. 单聊会话定向消息分发包含发送者

--------------
Release note 1.1.1:
1. 当用户获取群组信息，可以根据返回信息判断是否在群中。
2. 搜索用户时，过滤掉已删除用户
3. 支持单聊消息的定向消息
4. 解决关闭roaming，且message.compensate_time_limit配置为-1时，首次消息无法同步的问题
5. 解决禁止客户端群操作flag 解析错误

--------------
Release note 1.1:
1. Channel API添加检查用户是否订阅接口
2. Server API发送消息时添加检查对象是否存在
3. Channel API添加修改频道菜单接口
4. 内置对象存储的优化

--------------
Release note 1.0:
1. SDK检查工具添加help命令
2. 去掉短连接端口被网络爬虫扫描输出的异常日志
3. API支持创建组织群
4. 添加配置禁止客户端进行某些群操作
5. 解决route请求时可能不能正确携带推送类型的问题
6. 会议成员离开加上离开原因
7. 添加停止程序的命令
8. 添加RTP Forward的API

--------------
Release note 0.99:
1. API获取用户信息时，过滤掉已经删除用户。
2. 消息转发功能支持设置include/exclude功能，支持按区间过滤。
3. 添加新的群组类型：组织群。组织群适用于公司部门群场景。
4. 内置对象存储支持绝对路径和相对路径配置
5. 当开启禁止陌生人聊天时，可以添加例外人员
6. 解决user setting并发问题
7. 添加可选的android平台签名验证功能。

--------------
Release note 0.98:
1. 解决linux多端登录错误问题
2. 添加获取用户群组和共同群组功能
3. 解决windows下脚本编码错误问题

--------------
Release note 0.97:
1. 升级部分依赖库到最新版本
2. 添加名片消息默认推送信息
3. 添加deb和rpm格式软件包

--------------
Release note 0.96:
1. 文章消息添加摘要字段
2. 单聊消息支持server api只发送给其中一方
3. Server api添加会议录制/停止录制接口

--------------
Release note 0.95:
1. 添加按照用户删除会话接口
2. 升级netty版本为4.1.68.Final
3. 公众号文章添加摘要字段

--------------
Release note 0.94:
1. Server SDK添加删除会话接口
2. 解决内置对象存储某些类型文件返回content-type错误问题
3. 添加server api检查是否订阅频道接口
4. 添加超频检查忽略信令
5. gson单例化，且关掉html转义。

--------------
Release note 0.93:
1. Server API添加订阅频道功能
2. 短链接端口添加HEAD方法处理
3. Server SDK的HTTP Client调整部分参数，解决出现连接错误问题
4. 添加敏感词回调功能

--------------
Release note 0.92:
1. SDK部分POJO对象添加无参数构造函数解决某些反序列化工具失败问题。
2. 频道只有在auto属性为1时，才会回调函数及订阅事件。
3. 添加配置，在关闭消息漫游时，客户端首次登录同步多久时间之内的消息。
4. 频道添加菜单属性。

---------------
Release note 0.91:
1. 创建群组时检查群组类型的有效性。
2. 添加配置消息回调时是否带上客户端信息。
3. 添加群组成员时，检查是否已经在群组中。

---------------
Release note 0.90:
1. mysql-connector-java版本升级到8.0.28
2. 配置文件中去掉绑定IP的配置，防止误操作
3. 解决客户端触发限频多调用一次callback的错误。

---------------
Release note 0.89:
1. gson依赖库升级到2.8.9
2. log4j升级到2.17.2
3. 解决SDK部分响应反序列化失败的问题
4. 添加配置允许PC多端

---------------
Release note 0.88:
1. 添加部分数据库表的索引。
2. 优化频道订阅关系的缓存处理。
3. 添加支持开放平台的接口。
4. 机器人和频道回调消息中添加缺失信息。
5. 频道automic状态为1时，都不发送消息给owner。
6. 机器人api添加获取机器人信息的接口。
7. 解决server api撤回单聊消息时发送方无法撤回的问题

---------------
Release note 0.87:
1. 优化pc在线状态逻辑。
2. 添加配置当删除用户时使用的用户昵称。
3. 添加获取单条消息接口。

---------------
Release note 0.86:
1. Server SDK获取在线用户结果的变量改成公开

---------------
Release note 0.85:
1. 消息只能撤回一次，不能重复撤回。
3. gzip压缩避免内存泄漏

---------------
Release note 0.84:
1. 定向消息，如果有多于一个收件人，只保存第一个收件人，避免定向消息被远程消息拉取下来。
3. 内置文件存储，是否内存避免内存泄漏。

---------------
Release note 0.83:
1. 解决多端登陆时，处理好友请求状态同步问题
2. SDK会议接口中添加permanent参数，可以创建永久保存会议。

---------------
Release note 0.82:
1. 修正PC在线状态逻辑错误问题
2. 销毁用户时，清理所有信息
4. SDK中添加更新消息的接口（仅专业版支持）
5. SDK中添加获取在线用户数据（仅专业版支持）

---------------
Release note 0.81:
1. 修正PC在线状态某些特殊情况下错误问题
2. 写入用户信息关系表时带上会话信息

---------------
Release note 0.80:
1. 全局频道或者可以给非订阅用户发送消息的频道允许非订阅用户回复消息
2. Server API获取群组成员带上群组成员加入时间
4. 支持平板端接入
5. 解决有时PC在线状态错误问题
6. log4j2升级到2.17.1

---------------
Release note 0.79:
1. 频道API发送消息时，如果automic则不发送给频道主
2. 修改UserSetting表的key字段长度
3. 敏感词过滤跳过链接，链接内的词语不过滤敏感词
4. 销毁聊天室时，清理相关数据
6. 加入聊天室时判断是否聊天室已经清理
7. 升级log4j到log4j2

---------------
Release note 0.78:
1. 添加是否允许机器人自定义群通知的配置
2. 修正禁止存储searchablecontent时的错误问题
3. 解决API修改群组或用户为空时的异常问题
4. 当用户修改自己信息时，通知其他的更新自己的信息
5. MySQL数据字段中的关键字改为大小写敏感
7. 使用新的id生成方法，生成9为数字小写字母组成的id。

---------------
Release note 0.77:
1. Server API添加获取频道信息，销毁频道，销毁机器人功能。
2. 解决频道API发送中文乱码问题。
3. 解决频道拉取远程消息错乱问题。
4. 添加频道无密钥的错误码。
5. 解决频道和机器人API高压力下错乱问题。

---------------
Release note 0.76:
1. 为聊天室单独添加拉取远程消息开关。
2. 获取远程消息支持过滤消息类型。
3. 推送、消息、回调等事件HTTP请求失败打印提示信息。

---------------
Release note 0.75:
1. 修正有时欢迎语排序乱序问题。
2. 机器人禁止获取IM token。

---------------
Release note 0.74:
1. 发送好友请求时，如果已经是好友了，返回已经是好友的错误码
2. 修正通过server api修改用户昵称没有通知客户端的问题
3. 添加限频配置项

---------------
Release note 0.73:
1. 优化客户端断开连接功。
2. 解决双方同时添加好友，后接受的失败的问题。

---------------
Release note 0.72:
1. 添加提醒消息回调
2. 优化客户端断开连接功能
3. 支持客户端lite模式

---------------
Release note 0.71:
1. 添加多端登录被踢下线的错误码
2. 添加VOIP免打扰功能
3. 消息撤回推送功能
4. 推送只有pushData，没有pushContent也要推送

---------------
Release note 0.70:
1. 修改敏感词过滤问题
2. 添加修改好友附加信息的server api

---------------
Release note 0.69:
1. 完善会议API
2. 解决配置文件中特殊字符的问题
3. 添加简单的健康检查API

---------------
Release note 0.68:
1. 去掉脚本中GC日志输出，减少无效日志。
2. 添加配置是否加密存储消息。
3. 当接受好友请求时，如果请求原因为空，不发送消息。
4. 添加开关，当pc/web在线时移动端默认是否接收通知。
5. 解决文件不存在时的异常日志。

---------------
Release note 0.67:
1. Server API发送好友强制参数错误问题
2. 添加群组成员附加信息字段
3. 添加配置，允许加入聊天室不存在时自动创建
4. 解决Server API创建群组，部分参数无效问题
5. Server SDK链接复用问题修正

---------------
Release note 0.66:
1. Server SDK支持多个机器人账户

---------------
Release note 0.65:
1. 添加分段更新好友/好友请求/用户设置功能，防止相关数据太大无法更新下来
2. 同步Server SDK中关于会议的接口
3. 修正Server API设置好友请求被限制的问题

---------------
Release note 0.64:
1. 修正0.62版本重构引入的短链接错乱问题
2. 添加server api获取群组信息缺失字段
3. 添加配置项，可以为内置存储配置远程服务地址

---------------
Release note 0.63:
1. 实现频道权限功能
2. 配置文件中打开默认开启新好友欢迎语模式
3. 客户端更新用户设置时，不用再通知当前端
4. 群成员和好友请求中添加Extra字段

---------------
Release note 0.62:
1. 修改限频逻辑，server API，机器人api，频道api和客户端的限频要分开，避免server api超频导致客户端无法使用。
2. 添加关闭群主/群管理撤回用户权限的开关。
3. 对部分代码进行了重构。

---------------
Release note 0.61:
1. 升级缓存Hazelcast版本

---------------
Release note 0.60:
1. 解决server api修改群组信息失败问题

---------------
Release note 0.59:
1. 当server api未签名时，返回未签名的错误码
2. 当群成员离开群后再次加入群时，修改群成员createTime为最后一次加入时间
3. 解决自定义群组操作通知无效的问题
4. 群组修改extra时，发送通知给客户端
5. server api修改用户信息时添加修改name属性能力
6. 为user messages表添加会话信息及索引等
8. 内置测试存储文件上限提升到200MB

---------------
Release note 0.58:
1. 多人音视频添加成员也需要推送消息
2. 添加禁言时允许发送的消息类型配置
3. 添加频道相关api

---------------
Release note 0.57:
1. 修正某些情况下群组成员变更无法同步到客户端的问题

---------------
Release note 0.56:
1. 修正同一个设备登录多个用户的问题
2. 优化启动脚本，可以在bin目录下执行

---------------
Release note 0.55
1. 添加更新用户api接口

---------------
Release note 0.54:
1. 添加对接第三方敏感词处理接口
2. 添加消息转发类型过滤参数
3. 去掉对客户端最大版本号的限制，可以兼容未来版本客户端
4. 去掉默认敏感词

---------------
Release note 0.53:
1. 修正当敏感词策略为忽略时敏感词消息会从历史消息功能中拉取的问题
2. 修正创建群组时，如果没有把owner加入到群成员列表中，服务器自动加入群成员列表，但count计数时没有加入的问题
3. 添加获取两个用户之间用户关系的接口
4. 解决某些情况下群组成员计数不准确的问题

---------------
Release note 0.52:
1. 添加配置是否同步用户信息中敏感信息到客户端
2. SDK添加会话置顶功能
3. 屏蔽掉日志中消息内容的打印，防止日志泄漏聊天内容
4. 添加机器人进行群操作的功能
5. 添加机器人更改机器人回调地址的接口
6. 创建群组时，如果群主不在成员列表中自动加入群主
8. 禁止客户端直接修改自己的电话号码，只能通过server api修改自己的电话号码
9. 推送接听信息到客户端
10. 优化客户端被踢下线的逻辑
11. 内置存储下载时带上content-type字段
12. 添加历史消息自动清除功能，3年以上的历史消息自动删除

---------------
Release note 0.51:
1. 修改群成员昵称时，发送通知消息
2. 添加异常报警功能
3. 修正清除客户端清除掉数据库，无法获取自己发送消息的问题
4. 解决某些特殊情况下，用户session错误问题
5. 优化多端客户端被踢下线或者pc被手机踢下线的功能

---------------
Release note 0.50:
1. 解决分页查找用户或者频道，查找翻页错误问题
2. 添加是否允许客户端发送群操作通知的开关
3. 添加禁止客户端发送消息类型的配置，可以配置禁止客户端发送特定类型消息
4. 添加设置群成员昵称接口
5. 添加配置，当退群或者踢人时是否发送显式通知消息

---------------
Release note 0.48
1. 推送数据中加上用户id
2. server sdk发送消息接口添加定向用户参数
3. 状态消息改为可以同步到发送者其它端
4. 支持jdk15
5. 添加撤回组播和广播消息接口
6. 解决用户关系不存在时无法备注的问题

---------------
Release note 0.48
1. 同步专业版数据库

---------------
Release note 0.47:
1. 添加按照时间段免打扰功能
2. 解决服务器压力过大时消息id可能重复问题
3. 解决当拉取历史消息时，如果中间几个月没有发过消息无法拉取消息问题
4. 移除掉无用的fastjson库，避免系统安全警告
5. 添加群组/用户信息/频道信息变更通知

---------------
Release note 0.46:
1. Server api获取用户好友以好友状态时间排序
2. 增加Server api发送好友请求的功能
3. 解决server api删除好友时没有发送通知的问题
4. 添加PC在线时是否移动端推送的开关
5. 在频道或机器人没有密钥的情况下禁止使用api
6. 添加默认文件助手用户
7. 支持群主或群管理修改群成员昵称的功能

---------------
Release note 0.45:
1. 修正golang server api请求兼容问题。
2. 修改两个用户互相发送好友请求时，丢失其中一条的问题。
3. 解决server api删除好友失败的问题。

---------------
Release note 0.44:
1. 解决server api修改群成员昵称不生效的问题
2. 添加服务器端允许撤回时间最长限制，仅对普通用户有效，系统管理员/server api/群管理员可以任何时候撤回消息
3. 获取群成员接口检查权限，不在群组中成员无法获取群成员。
4. 撤回消息的extra中添加更多原始消息内容
5. 用户在线状态回调接口添加包名
6. 创建群组时，按照请求列表顺序对成员排序
7. 修正server SDK中的错误测试例
9. Server SDK中添加对专业版白名单的支持
10. 修正server api中设置好友状态不能实时同步到客户端的问题
11. 修正客户端创建群组时，群主的群成员状态错误问题

---------------
Release note 0.43:
1. 修改撤回消息，extra信息填写错误问题
2. 修正黑名单设置后无法取消问题
3. 推送信息添加push_data字段
4. 消息过期时间问题修正
5. server api创建群组时，添加更多的可选参数
6. 修正推送数据中计数不准确问题

> *** 0.42 版本增加了群成员数限制，默认为2000。如果您想修改默认值，可以在升级版本之后，修改t_setting表，把默认的大小改为您期望的。另外修改t_group表，把已经存在的群组max_member_count改成您期望的，然后重启。***

--------------

Release note 0.42:
1. 当接受好友请求时，如果已经时好友，返回已经是好友的错误码
2. 解决SDK中修改群信息缺少自定义通知内容的问题
3. Server api获取群成员过滤掉已经删除成员信息
4. SDK增加群内禁言功能（仅专业版支持）
5. 转发消息带上消息id和时间戳
6. 对拉黑server api添加参数校验
7. 群成员添加加入时间属性
8. 音视频电脑挂掉消息不增加未读消息计数
9. 添加对群成员最大限制控制，默认为2000
10. 普通成员可以修改群extra属性。
12. 修改使用server api操作群时通知错误
> *** 0.42 版本增加了群成员数限制，默认为2000。如果您想修改默认值，可以在升级版本之后，修改t_setting表，把默认的大小改为您期望的。另外修改t_group表，把已经存在的群组max_member_count改成您期望的，然后重启。***

--------------

Release note 0.41:
1. 添加手机控制PC下线功能
2. 添加是否保存searchable_content字段的配置
3. 增加移动端退出不清除session且不推送消息操作（现在移动端退出有3个可选参数，断开连接有消息就推送/断开连接清掉服务器session/断开连接不推送且保留session）
4. SDK添加对物联网设备的支持（物联网接入功能仅专业版有效)

--------------

Release note 0.40:
1. 修正拒绝好友错误问题。
2. 添加聊天室API。
3. 添加禁止用户搜索和禁止添加好友的配置。
4. 升级mysql connector到8.0.19版本。
5. 添加PC在线状态通知。
6. 在撤回消息中加上被撤回的内容。
7. 修改设置黑名单失败的问题。
8. 添加sticker bucket的支持。

--------------

Release note 0.39:
1. 修正群内定向消息发送错误问题
2. 添加好友成功多语言处理
3. 修正拒绝好友请求的错误问题
4. 修正内置测试文件服务器部分错误问题
5. 添加机器人根据电话号码或用户名查询用户接口
6. 修正Server SDK中的用户关系错误问题
7. 修改获取用户在线状态错误问题
8. 修正用户信息中社交字段无法更新问题
9. 在好友关系中添加extra字段
10. 给token添加过期时间配置
11. 添加获取聊天室成员的api
13. 修改被封禁用户连接失败的错误码
14. 添加好友请求推送

--------------

Release note 0.38:
1. 添加好友请求过期时间配置，可以配置重复发起好友请求时间
2. 修正黑名单策略为1时发送消息仍失败的错误
3. 增加sdk中聊天室创建接口，增加sdk聊天室相关测试代码
4. 修改创建用户时不能指定用户类型的错误
5. 增加日志默认大小
6. 修改频道发送消息给指定用户错误问题
7. 添加检查token是否正确的api
8.  修正频道创建错误，修正频道发送消息错误

---------------
Release note 0.37-1:
1. 修复聊天室逻辑错误

---------------
Release note 0.37
1. 修正sdk中好友相关接口的错误。
2. 添加是否运行陌生人聊天的配置。
3. 增加获取备注的api。
4. 增加用户在线状态回调功能。
5. 优化聊天室逻辑，添加用户超时退出聊天室配置和是否发消息自动加入配置。
6. 禁止发送消息给被封禁用户。
7. 判断是否devicetoken重复时，加上包名条件。
8. 添加好友请求相关时间配置。
9. 添加频道SDK

---------------
Release note 0.36
1. 修正某些特殊情况下群组操作不同步的问题
2. 优化用户的session管理

---------------
Release note 0.35
1. 修正0.34版本引入的无法拉取消息的严重问题
2. 修正单条消息大于512K时无法收取消息问题
3. 修改群主被添加进群状态错误问题
4. 添加新的搜索类型，新增加按照账户或按照电话号码搜索

---------------
Release note 0.34
1.，添加销毁用户server接口和功能
2，添加获取用户群组列表接口和功能
3，添加用户已删除状态
4，解决IM服务调用推送服务内存泄漏问题
5，添加服务器为用户缓存消息条数配置


--------------
Release note 0.33

1. 优化用户session逻辑

--------------

Release note 0.32:
1. 排除重复的device token
2. 添加关闭多端功能。

--------------

Release note 0.31:
1，解决了黑名单的同步的问题
2，解决了黑名单发送消息成功的问题

--------------

Release note 0.30:
1，重构了黑名单功能，好友关系和黑名单关系分开
2，添加敏感词后立即生效
3，转让群主后，群成员状态问题修复
4，修改离线消息包大小，从1M改为512K
