## 注意事项：
#1. 配置文件修改是不能直接生效的，需要重启后才能生效。
#2. Linux下使用windows格式配置文件会出错，包括本文件，c3p0文件，hz配置。常见于在windows系统上修改，然后上传到linux服务器，可以在linux服务器上直接修改或者上传到linux服务器后转换成unix格式，具体转换方法请百度。

#*********************************************************************
# Server configuration
#*********************************************************************
#服务器的接入IP。给客户端提供是${server.ip}和${http_port}。
#客户端会从${http_port}端口获取到长链接端口。这个地址一定要改成客户端可以访问到的IP地址
#（如果您部署云服务器上或者具有独立公网出口的服务器上，请改为对应的公网IP；如果您部署在在内网环境下，在内网使用，这个地方改成内网地址）
server.ip 0.0.0.0

##原生客户端长链接端口。如果使用4层的反向代理或者类似组件，需要设置长链接保持时间为10分钟。不支持7层代理。
port 1883

##客户端短链接端口，客户端固定使用80端口不能修改。
##如果是国企事业单位等因为政策原因无法使用80端口，可以联系野火解决。
http_port 80

#管理端口
http.admin.port 18080

##节点ID，当集群部署时，一定不能有重复。
node_id 1

##绑定IP，不要打开，除非有特殊需求才可以打开
#host 0.0.0.0

##本地绑定端口
local_port 80

## 是否使用内置DB。0使用mysql；1使用h2db。
## MySQL需要把事物隔离级别改成"Read committed"，使用命令来修改"set global transaction_isolation='read-committed';"，其他数据库默认已经是这个级别不用修改
## 如果使用MySQL，请在c3p0.xml文件中配置JDBC信息。
## 如果是MySQL5.7，需要开启长索引的支持，在数据库中执行命令:"set global innodb_large_prefix=on;"。其他版本默认支持，不用修改。
## 如果使用MySQL 8.0.30以上版本，有个特性是创建表时如果没有主键将会自动创建一个主键"my_key_id"，如果此功能打开，在执行mysql脚本V19__add_id_for_sensitive_word.sql时将会报错，提示不能2个主键。
## 解决办法就是运行出错后删除掉系统自动创建的主键"my_key_id"，然后继续执行。
embed.db 1

## 是否自动清理历史消息记录，当为true时，IM服务会定时扫描t_messages_x和t_user_messages_x表，清除掉3年前的历史消息。
## 如果不开启自动清理，需要手动清理t_messages_x和t_user_messages_x表内的历史数据，保存数据不能超过3年。
## 如果需要消息保存更长时间，可以把临近销毁的消息转储到别的数据库，客户端在拉取不到服务器消息时，再去转储的数据库拉取。
## 使用mongodb时次开关无效（因为mongodb设置了消息过期时间，会自动清理，仅专业版支持mongodb）。
db.auto_clean_history_messages true

## h2db数据库的路径，默认为程序目录下的，可以指定其他目录。如果使用MySQL，可以忽略此配置
h2db.path ./h2db/wfchat

##服务器管理接口密钥。如果修改此端口需要同步修改使用管理API的地方，比如应用服务。
http.admin.secret_key 123456

##服务器API接口参数是否检查时间。当设置为false时，所有的请求会检查时间的有效性；当设置为true时，可以在http.admin.secret_key保持不变的情况下，使用固定的服务API签名
##nonce = "76616", timestamp = "1558350862502", sign = "b98f9b0717f59febccf1440067a7f50d9b31bdde"
http.admin.no_check_time false

##用来生产im token的私钥，只在服务器使用，客户端不用。正式使用时为了安全一定要修改这个值，切记切记
token.key testim

##token的过期时间，单位为毫秒，默认为无限期。如果需要设置无限期，客户端上一定需要加上token过期的处理。token过期的处理请参考文档的常见问题
##token.expire_time 2592000000

##客户端（目前只有android）的应用签名（一个应用最取第一个签名），如果有多个签名(多个客户端)，用英文逗号分开。获取签名方法请参考文档：https://docs.wildfirechat.cn/blogs/签名验证.html
#connect.client_signature_list eNkezQ1g9OsnhfLSFUY1vzKDzhs=,xykezQ1g9OsnhfLSFUY1vzKDzhs=

##是否拒绝空签名的android客户端， 默认为true。当有旧的客户端需要兼容时改成false，当所有都是新客户端时，改成true。
#connect.reject_empty_signature true

##是否使用AES256加密。默认客户端和服务器都是AES128加密，如果需要开启AES256，需要客户端和IM服务同时开通。
#encrypt.use_aes256 true

##当创建一个对象时（用户，群组，频道，机器人等），如果没有传入ID，野火会为这个对象生成一个ID。如果id.use_uuid为true，会使用uuid作为对象ID，否则会使用野火短ID，默认为false。
## 短ID有个很大的风险是容易被人穷举进行攻击。建议用UUID。
id.use_uuid true

##首次登录，是否接收之前的历史消息。
##0 不接收历史消息，只接收${message.compensate_time_limit}毫秒以内的消息，由于服务器没有保存已经收取记录，所以如果有超过${message.compensate_time_limit}毫秒之前未收取的消息也不会收取下拉；
##1接收，会接收 message.max_queue 配置的条数的历史消息
message.roaming 0

## 消息补偿时限，当${message.roaming}为0时，会同步时限以内且小于${message.max_queue}的消息，默认时限为5分钟。当${message.roaming}为1时，会接收 message.max_queue 配置的条数的历史消息。
message.compensate_time_limit 300000

##是否开启拉取远程历史消息。如果为1，客户端在会话内如果本地消息读取完了，可以下拉继续加载在服务器上的该会话的消息；如果为0则不能。
message.remote_history_message 0

##服务器为每个用户缓存的消息数量。这个值改得太大，拉取消息时间变长，另外会占用大量内存。
message.max_queue 1024

##是否开启拉取聊天室远程历史消息。如果为1，可以下拉继续加载在服务器上的该聊天室的消息；如果为0则不能。默认为1
message.chatroom_remote_history_message 1

##是否禁止陌生人聊天
message.disable_stranger_chat false

##当禁止陌生人聊天时，允许聊天的用户id，比如管理员或者文件传输助手等。用户id以英文逗号分割。
message.allow_stranger_chat_list admin,FireRobot,wfc_file_transfer

##当禁止陌生人聊天时，允许聊天的线路。
#message.allow_stranger_line 100,101

##黑名单策略，0 发送失败，返回被拉黑的错误码；1 发送成功但消息被服务器直接丢弃
message.blacklist.strategy 0

##是否禁止服务器端消息搜索，该功能暂未实现。目前的影响是如果打开，则存储消息时不单独保存_searchable_content字段
message.disable_remote_search  false

##是否数据库中加密消息内容。注意这个开关在服务运行起来后不能改变了，避免读取数据库时无法恢复历史消息。
##如果要避免数据库中明文存储消息内容，请打开这个开关和message.disable_remote_search开关
message.encrypt_message_content  false

##允许客户端撤回用户自己发送的消息时限，单位是秒。0是不允许撤回；-1是无限制撤回。
##Server API不受此限制，可以撤回任意时限内的消息
##群组管理员或群主撤回群成员消息不受此限制，可以撤回任意时限内的消息。但管理员或群主撤回自己的消息还是需要准守此时间限制。
##修改此参数需要同步修改客户端UI，客户端UI代码有判断撤回是否显示的逻辑。
message.recall_time_limit 120

##禁止群主/群管理员撤回群内消息，关闭时群主/群管理员可以撤回任意时间段内群内成员的消息。
message.disable_group_manager_recall false

##禁止客户端发送消息类型，消息类型以逗号分开，下面3个值为示例，可以删除。
##一般用于重要类型消息，比如交易、充值等，禁止客户端发送，仅限于服务器发送，提高安全性。
#message.forbidden_client_send_types 3999,4000,4001

## 当禁止发送消息时，允许例外发送的消息。这种一般用户需要被动回应的消息。比如群中被禁言的用户，他无法主动发起音视频，但可以接听未被禁言用户的音视频
## 因为音视频使用了消息作为信令，所以就需要允许发送音视频信令。如果开发了其它场景也需要被动回应消息，可以加在这里。
## 黑名单时允许发送的消息类型
message.blacklist_exception_types 401,402,403,404,405,407,408,410,411
## 群禁言或者聊天室或者频道禁言时允许发送的消息类型
message.group_mute_exception_types 401,402,403,404,405,407,408,410,411
## 用户被全局禁言时允许发送的消息类型
message.global_mute_exception_types 401,402,403,404,405,407,408,410,411

## 离线用户推送过期天数，0是永不过期，建议配置为7天。
message.push_expired_days 7

## 当会话静音/全局静音/PC在线静音时，普通消息都不会有推送，如果有某些消息类型需要此时保持推送，可以配置到这里。此配置对消息免打扰时段无效。
## 此配置只影响远程推送，需要客户端处理客户端在线且在后台收到此消息的处理，需要针对这些消息类型加上本地通知。
## 强制推送消息的MessagePayload中pushContent和pushData至少要存在一个才可以推送。
## 消息类型以英文逗号分割。
#message.force_push_types 401,402

## 群发消息(包括server api群发和全局频道群发)时，目标用户是否来自于t_user表。如果使用野火托管用户信息，请设置为true，否则设置为false。
message.broadcast.target_from_user_table true

## 消息转发功能开启时，开关是否不转发server api接口消息。
## 当为true时，不转发server api发送的消息；当为false时，转发server api发送的消息。
message.no_forward_admin_message false

## 消息转发功能开启时，转发信息是否带上用户clientId和平台类型。
message.forward_with_client_info false

## 消息转发功能开启时，转发信息是否带上发送者的用户信息
message.forward_with_sender_info false

## 消息转发功能开启时，转发信息是否带上目标的信息，单聊时是对方用户信息，群聊时是群组信息，频道时是频道信息
message.forward_with_target_info false

## 允许发送单聊消息给被封禁用户
message.allow_send_to_forbidden_user false

## 机器人回调信息是否带上用户clientId和平台类型。
robot.callback_with_client_info false

## 机器人回调信息是否带上发送者的用户信息
robot.callback_with_sender_info false

## 机器人回调信息是否带上目标的信息，单聊时是对方用户信息，群聊时是群组信息，频道时是频道信息
robot.callback_with_target_info false

## 频道回调信息是否带上用户clientId和平台类型。
channel.callback_with_client_info false

## 频道回调信息是否带上发送者的用户信息。
channel.callback_with_sender_info false

## 频道回调信息是否带上频道信息
channel.callback_with_target_info false

## 频道回调新方式，在新方式下，如果有callback地址就会总是回调客户端发送的消息（包括owner和订阅者）。automatic属性控制订阅者发送的消息是否发给owner， 0发，1不发。
## 旧的方式是，automatic为0时，订阅者的消息发给owner，不转发消息到callback地址。为1时，把订阅者的消息转发给callback地址，不发给owner。
channel.new_callback_feature true

##禁止搜索用户
friend.disable_search false

##禁止按照昵称搜索用户。
friend.disable_nick_name_search false

##禁止按照用户id搜索用户，默认为true。
friend.disable_user_id_search true

##禁止发送好友邀请，通过server api添加好友不受此限制(force参数需要为true)
friend.disable_friend_request false

##好友请求重复发送的间隔，单位是毫秒，默认是7天，0为无限长期限。
friend.repeat_request_duration 604800000

##好友请求被拒绝后再次发送的间隔，单位是毫秒，默认是30天，0为不限制。不能小于friend.repeat_request_duration。
friend.reject_request_duration 2592000000

##好友请求过期时间，单位是毫秒，默认是7天，0为无限长期限。
friend.request_expiration_duration 604800000

##好友请求限制频率，一个用户24小时之内允许请求好友的次数，默认不限频。
#friend.request_rate_limit 30

##请求添加机器人为好友时，机器人是否自动接受，默认为true
friend.robot_auto_accept true

## 聊天室观众空闲退出时间，单位为毫秒，默认为15分钟，0为永远不退出
chatroom.participant_idle_time 900000

## 用户向聊天室发送消息，自动加入聊天室
chatroom.rejoin_when_active true

## 当一个聊天室不存在时，如果有用户加入就自动创建
chatroom.create_when_not_exist true

## 一个用户同一时刻只能加入一个聊天室。
## 当一个客户端加入一个聊天室时，如果这个客户端已经加入另外一个聊天室了，会自动退出之前的聊天室。
## 当一个客户端加入一个聊天室时，如果这个用户的其他端已经加入一个聊天室了，kickoff_other_platform为true时会自动退出之前的聊天室，为false时会返回225的错误码。
## 当自动退出聊天室时，客户端不会收到任何通知，只是不会再收到消息。
chatroom.kickoff_other_platform true

## 是否允许群主不受限制地撤回自己发送的消息。false可以${message.recall_time_limit}撤回自己的消息；true可以撤回任意时间自己的消息。默认为true。
group.allow_owner_recall_self_msg true

## 是否允许群管理员不受限制地撤回自己发送的消息。false可以${message.recall_time_limit}撤回自己的消息；true可以撤回任意时间自己的消息。默认为false。
group.allow_manager_recall_self_msg false

## 是否允许客户端发送操作群的自定义群通知消息
group.allow_client_custom_operation_notification true

## 是否允许机器人发送操作群的自定义群通知消息
group.allow_robot_custom_operation_notification true

## 当群成员退出或者被移除时，是否同时给管理员和相关人员发送显式通知消息。
## 0 不发送，1 退群时发送，2 踢人时发送，3 退群和踢人时都发送。
## 如果应用需要退群或者踢人不通知普通成员，请设置此参数为3，然后修改客户端踢人或退群消息的flag为not_presist。
group.visible_quit_or_kickoff_notification 0

## 禁止客户端群操作。第1位是禁止创建群组，第2位是禁止销毁群组，3位禁止加入群，4位禁止退出群，5位禁止邀请群成员，6位禁止移出群成员，7位禁止转移群，8位禁止设置群管理员，9位禁止白名单处理，10位禁止群禁言，11位禁止修改群组信息，12位禁止群成员禁言。
## 如果有客户端有被破解的风险，可以禁止客户端群操作，然后通过Server API操作。Server API不受限制。
#group.forbidden_client_operation 0xFFF

## 是否禁止陌生人拉人入群，默认为false
group.disable_stranger_invite false

## 群组信息是否标记删除
group.enable_mark_deletion true

## 隐藏用户信息属性类型，2是性别，3是电话号码，4是邮箱，5是地址，6是公司信息，7是社交账号，8是Extra信息。当需要隐藏多个时以英文逗号分开;
## 本配置是为了提高安全性，避免把对应信息同步到客户端，这样就能防止程序被破解从而拿到用户关键信息。仅对客户端有效，server api不受影响。
## 注意，修改本配置不会对已经同步的信息产生影响。
#user.hide_properties 3,4

# 好友/好友请求/用户设置 这三类数据同步的最大条目数。之前是客户第一次登录时，会把所有的对应数据一次下载到客户端，但如果有特别多的信息，会导致无法同步到客户端，
# 因此需要分段多次同步，此开关定义了每次同步的数据的条数。此需要客户端支持，客户端SDK在2021.5.25日后支持此功能。如果确认所有客户端都在这个日志之后才可以打开此设置。
#sync.data_part_size 1000

## 是否关闭api/version接口。api/version方法可以用来检查IM服务版本，上线确认无问题后，可以关掉这个接口。
#http.close_api_version false

#*********************************************************************
# 限频配置
#*********************************************************************
##API接口限频设置，时间是10秒内允许的请求次数。
##管理api频率限制，默认是10秒10000次。
http.admin.rate_limit 10000
##机器人api频率限制，默认是10秒1000次，按照机器人ID区分。
http.robot.rate_limit 1000
##频道api频率限制，默认是10秒1000次，按照频道ID区分。
http.channel.rate_limit 1000

##客户端请求频率限制，默认是5秒100次。通常情况下不要修改这个值。
client.request_rate_limit 100

#*********************************************************************
# Media server configuration
#*********************************************************************
##是否使用七牛云存储。1使用七牛；0使用内存文件服务器。默认的七牛账户信息不可用，请在七牛官网申请账户并配置
##专业版另外支持阿里云存储和野火私有存储，关于野火私有存储请参考：https://github.com/wildfirechat/WF-minio
##关于对象存储的详细信息请参考：https://docs.wildfirechat.cn/server/oss.html
media.server.use_qiniu 0

# qiniu media server configuration
## 上传地址，不同的区域，上传地址也不同，请注意现在正确的地址
qiniu.server_url  http://up.qbox.me
qiniu.access_key tU3vdBK5BL5j4N7jI5N5uZgq_HQDo170w5C9Amnn
qiniu.secret_key YfQIJdgp5YGhwEw14vGpaD2HJZsuJldWtqens7i5
## bucket名字及Domain
qiniu.bucket_general_name media
qiniu.bucket_general_domain http://cdn.wildfirechat.cn
qiniu.bucket_image_name media
qiniu.bucket_image_domain http://cdn.wildfirechat.cn
qiniu.bucket_voice_name media
qiniu.bucket_voice_domain http://cdn.wildfirechat.cn
qiniu.bucket_video_name media
qiniu.bucket_video_domain http://cdn.wildfirechat.cn
qiniu.bucket_file_name media
qiniu.bucket_file_domain http://cdn.wildfirechat.cn
qiniu.bucket_sticker_name media
qiniu.bucket_sticker_domain http://cdn.wildfirechat.cn
qiniu.bucket_moments_name media
qiniu.bucket_moments_domain http://cdn.wildfirechat.cn
qiniu.bucket_portrait_name storage
qiniu.bucket_portrait_domain http://cdn2.wildfirechat.cn
qiniu.bucket_favorite_name storage
qiniu.bucket_favorite_domain http://cdn2.wildfirechat.cn

##媒体类型分类
#Media_Type_GENERAL = 0,
#Media_Type_IMAGE = 1,
#Media_Type_VOICE = 2,
#Media_Type_VIDEO = 3,
#Media_Type_FILE = 4,
#Media_Type_PORTRAIT = 5,
#Media_Type_FAVORITE = 6,
#Media_Type_STICKER = 7,
#Media_Type_MOMENTS = 8

# local media server configuration
# 本地媒体服务器配置。
local.media.storage.root media
# 如果使用内置文件存储，文件上传后地址默认为 http://server.ip:http_port/fs/5/2021/03/27/08/filename。fs目录下按照类型存放文件，媒体类型见上面注释，比如5就是Media_Type_PORTRAIT，是客户端的头像文件。
# 如果需要使用nginx添加https支持，请打开下面配置，这样客户端得到的文件地址为 https://example.com/media/fs/5/2021/03/27/08/filename。
# 需要nginx把请求从https://example.com/media/fs 转到 http://server.ip:http_port/fs。注意需要带上所有header。
# 尽管内置文件存储可以使用，但是我们还是建议使用专业级别的对象存储服务，社区版可以使用七牛，专业版另外支持阿里云和也是私有对象存储。详情请参考 https://docs.wildfirechat.cn/server/oss.html
# local.media.storage.remote_server_url https://example.com/media

# 是否支持任意多端登陆，为true时支持任意平台任意多个客户端同时登录；为false时每个平台只支持一个端登录，但不同平台可以同时登录。
# Android/iOS为移动平台，windows/mac/linux为pc平台，web为web平台，小程序为小程序平台。
# 建议使用false
server.multi_endpoint false

## 是否支持PC多端登陆，当server.multi_endpoint为true时，此开关无意义，当为false时，可以单独打开PC端多端登录。
server.multi_pc_endpoint false

## 是否支持PAD多端登陆，当server.multi_endpoint为true时，此开关无意义，当为false时，可以单独打开PAD端多端登录。
server.multi_pad_endpoint false

# 多平台连接状态通知（仅当multi_endpoint为false时有效），true时移动端可以收到pc或pad或web端登录的通知。
server.multi_platform_notification true

# 当pc或者web在线时，手机是否默认静音，默认为true。注意仅影响默认值，用户可以手动切换是否静音。
# 如果这里改为false，客户端那边需要同步修改pc在线默认通知状态，方法是client的setDefaultSilentWhenPcOnline函数。
server.mobile_default_silent_when_pc_online true

## 客户端是否支持kickoff事件。当客户端被其他端登录踢出时，如果此开关为false时，客户端协议栈上报secret_mismatch。如果此开关为ture时，客户端协议栈上报kicked_off。
## 客户端被踢时上报kicked_off是在2021.9.15之后才加上的。如果客户端协议栈全部为此日期之后的版本才可以打开此开关。
server.client_support_kickoff_event true

#*********************************************************************
# Push server configuration
#*********************************************************************
##推送服务项目地址：https://github.com/wildfirechat/push_server。
##安卓推送服务器地址
##注意回调是单线程回调，接收服务必须在同一内网，且异步处理快速返回，否则会有延迟问题！！
push.android.server.address http://localhost:8085/android/push

##苹果推送服务器地址
##注意回调是单线程回调，接收服务必须在同一内网，且异步处理快速返回，否则会有延迟问题！！
push.ios.server.address http://localhost:8085/ios/push

##鸿蒙推送服务器地址
##注意回调是单线程回调，接收服务必须在同一内网，且异步处理快速返回，否则会有延迟问题！！
push.harmony.server.address http://localhost:8085/harmony/push

#*********************************************************************
# 监控配置
#*********************************************************************
##异常事件产生回调
##注意回调是单线程回调，接收服务必须在同一内网，且异步处理快速返回，否则会有延迟问题！！
#monitor.exception_event_address http://localhost:8888/im_exception_event/

#*********************************************************************
# 各种事件回调
#*********************************************************************
##用户在线状态事件回调地址
##注意回调是单线程回调，接收服务必须在同一内网，且异步处理快速返回，否则会有延迟问题！！
#user.online_status_callback http://localhost:8888/im_event/user/online

##用户信息变动事件回调地址
##注意回调是单线程回调，接收服务必须在同一内网，且异步处理快速返回，否则会有延迟问题！！
#relation.relation_update_callback http://localhost:8888/im_event/user/relation

##用户信息变动事件回调地址
##注意回调是单线程回调，接收服务必须在同一内网，且异步处理快速返回，否则会有延迟问题！！
#user.user_info_update_callback http://localhost:8888/im_event/user/info

##消息转发地址
##注意回调是单线程回调，接收服务必须在同一内网，且异步处理快速返回，否则会有延迟问题！！
#message.forward.url http://localhost:8888/im_event/message
##需要转发的消息类型，当有多个时以英文逗号分割，可以用-来指定区间，注意区间不能太大，因为要把区间内的所有数字都放入到Set中。如果转发所有消息，请注释掉配置或者设置为空
#message.forward.types 1,2,3,5-10
##需要转发的消息类型，当有多个时以英文逗号分割，可以用-来指定区间，注意区间不能太大，因为要把区间内的所有数字都放入到Set中。
#message.forward.exclude_types 1,2,3,5-10

##敏感消息转发地址
##注意回调是单线程回调，接收服务必须在同一内网，且异步处理快速返回，否则会有延迟问题！！
#message.sensitive.forward.url http://localhost:8888/im_event/message

##提醒消息转发地址，@全体用户和@某个用户消息会回调此地址。
##此转发不受${message.forward.types}和${message.forward.exclude_types}限制。
##注意回调是单线程回调，接收服务必须在同一内网，且异步处理快速返回，否则会有延迟问题！！
#message.mentionmsg.forward.url http://localhost:8888/im_event/message

##撤回消息转发地址，当用户撤回消息会回调此地址。
##注意回调是单线程回调，接收服务必须在同一内网，且异步处理快速返回，否则会有延迟问题！！
#message.recallmsg.forward.url http://localhost:8888/im_event/recall_message

##设备信息转发地址
##注意回调是单线程回调，接收服务必须在同一内网，且异步处理快速返回，否则会有延迟问题！！
#things_message.forward.url http://localhost:8087/im_event/things/message

##群组信息变动事件回调地址
##注意回调是单线程回调，接收服务必须在同一内网，且异步处理快速返回，否则会有延迟问题！！
#group.group_info_update_callback http://localhost:8888/im_event/group/info

##群组信息变动事件回调地址
##注意回调是单线程回调，接收服务必须在同一内网，且异步处理快速返回，否则会有延迟问题！！
#group.group_member_update_callback http://localhost:8888/im_event/group/member

##频道信息变动回调地址
##注意回调是单线程回调，接收服务必须在同一内网，且异步处理快速返回，否则会有延迟问题！！
#channel.channel_info_update_callback http://localhost:8888/im_event/channel/info

##聊天室信息变动回调地址
##注意回调是单线程回调，接收服务必须在同一内网，且异步处理快速返回，否则会有延迟问题！！
#chatroom.chatroom_info_update_callback http://localhost:8888/im_event/chatroom/info

##聊天室成员变动回调地址
##注意回调是单线程回调，接收服务必须在同一内网，且异步处理快速返回，否则会有延迟问题！！
#chatroom.chatroom_member_update_callback http://localhost:8888/im_event/chatroom/member

#*********************************************************************
# Netty Configuration
#
# Linux systems can use epoll instead of nio. To get a performance
# gain and reduced GC.
# http://netty.io/wiki/native-transports.html for more information
#*********************************************************************
# 如果是linux系统，一定要打开下面这个参数，能大幅度提高性能。默认只支持x86_64架构。如果是arm64架构，可以按照项目说明来更新epoll SDK。其他架构不支持epoll。
#netty.epoll true

#*********************************************************************
# Sensitive configuration
#*********************************************************************
## 内置文本敏感词过滤处理方法，当命中敏感词后，消息会被记录到t_sensitive_messages表中，然后根据type类型做不同的处理，处理方式如下：
## 0 发送失败；1 发送成功但消息被服务器直接丢弃；2 命中的敏感词被替换成***然后正常发送；3 正常发送。
## 如果开启 message.sensitive.forward.url 配置，命中敏感词的消息还会被回调到这个地址
sensitive.filter.type 2

## 敏感词添加或者删除有2种方式，一种是通过Server API进行，这种方式会立即生效。另外一种方法是修改数据库，直接中数据库的t_sensitive_word表中处理
## 修改数据库的方法不会立即生效，IM服务会2个小时重新加载一次敏感词。所以如果直接修改敏感词表，需要等2个小时，或者重启才能生效。

## 如果内置敏感词库无法满足您的需求，可以开发部署独立敏感词审核服务。配置如下地址和消息类型，IM服务会把指定消息类型的消息回调到指定地址，内置的敏感词审核和敏感词转发功能就不再起作用了。
## 审核服务返回状态码200表示继续发送，如果需要替换内容，返回状态码200并且内容为替换后的内容。返回403表示不允许发送。其它错误继续发送。
## 独立审核服务地址，IM服务会post SendMessageData。如果需要修改消息内容，返回 MessagePayload。请参考应用服务中 "/message/censor" Mapping.
#sensitive.remote_server_url http://192.168.3.202:8888/message/censor
## 需要进行敏感词审核的消息类型，当有多个时以英文逗号分割
#sensitive.remote_sensitive_message_type 1,2,3
## 如果远程审核服务返回403，返回给发送端发送失败还是成功？实际上这个值影响发送响应是否等待审核结果，当为false时，消息发送给审核服务就立即返回成功给客户端。
## 当为true时，消息发送给审核服务，等待审核结果，如果结果为继续发送就返回给客户端为成功，结果为禁止发送就返回失败给客户端。
## 客户端操作的超时一般为10s左右，且考虑到复杂的网络情况，建议处理时间不要超过3s。建议这个值为false。
#sensitive.remote_fail_when_matched false
