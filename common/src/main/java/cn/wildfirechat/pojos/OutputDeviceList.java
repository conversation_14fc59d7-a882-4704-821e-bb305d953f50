/*
 * This file is part of the Wildfire Chat package.
 * (c) Heavyrain2012 <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

package cn.wildfirechat.pojos;

import java.util.List;

public class OutputDeviceList {
    private List<OutputDevice> devices;

    public List<OutputDevice> getDevices() {
        return devices;
    }

    public void setDevices(List<OutputDevice> devices) {
        this.devices = devices;
    }
}
