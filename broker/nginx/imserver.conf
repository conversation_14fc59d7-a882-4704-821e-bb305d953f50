upstream imserver  {
    server 127.0.0.1:8080; #Apache
}

server {
    listen 80;
    server_name  www.wildfirechat.cn;

    root   html;
    index  index.html index.htm index.php;

    #同步服务设置
    location /route {
        proxy_set_header  Host  $host;
        proxy_set_header  X-real-ip $remote_addr;
        proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass  http://imserver;
    }

    #客户端短连接业务
    location /im {
        proxy_set_header  Host  $host;
        proxy_set_header  X-real-ip $remote_addr;
        proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass  http://imserver;
    }

    #版本检查/api/version，可以删掉
    location /api {
        proxy_pass  http://imserver;
    }

    #内置文件上传和下载
    location /fs {
        client_max_body_size 200M;
        proxy_set_header  Host  $host;
        proxy_set_header  X-real-ip $remote_addr;
        proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass  http://imserver;
    }

    #机器人API
    location /robot {
        proxy_set_header  Host  $host;
        proxy_set_header  X-real-ip $remote_addr;
        proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass  http://imserver;
    }

    #频道API
    location /channel {
        proxy_set_header  Host  $host;
        proxy_set_header  X-real-ip $remote_addr;
        proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass  http://imserver;
    }
}
