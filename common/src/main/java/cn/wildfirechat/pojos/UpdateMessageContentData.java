/*
 * This file is part of the Wildfire Chat package.
 * (c) Heavyrain2012 <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

package cn.wildfirechat.pojos;

public class UpdateMessageContentData {
    private String operator;
    private long messageUid;
    private MessagePayload payload;
    private int distribute;
    private int updateTimestamp;
    private int meshLocal;

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public long getMessageUid() {
        return messageUid;
    }

    public void setMessageUid(long messageUid) {
        this.messageUid = messageUid;
    }

    public MessagePayload getPayload() {
        return payload;
    }

    public void setPayload(MessagePayload payload) {
        this.payload = payload;
    }

    public int getDistribute() {
        return distribute;
    }

    public void setDistribute(int distribute) {
        this.distribute = distribute;
    }

    public int getUpdateTimestamp() {
        return updateTimestamp;
    }

    public void setUpdateTimestamp(int updateTimestamp) {
        this.updateTimestamp = updateTimestamp;
    }

    public int getMeshLocal() {
        return meshLocal;
    }

    public void setMeshLocal(int meshLocal) {
        this.meshLocal = meshLocal;
    }
}
