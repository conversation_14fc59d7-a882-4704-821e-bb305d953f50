<assembly xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2 http://maven.apache.org/xsd/assembly-1.1.2.xsd">
    <id>bundle-tar</id>
    <formats>
        <format>tar.gz</format>
        <!--<format>dir</format>-->
    </formats>
    <includeBaseDirectory>false
    </includeBaseDirectory> <!-- disable the creation of root's distribution dir in the archive -->

    <fileSets>
        <fileSet>
            <directory>../broker/migrate/mysql/</directory>
            <outputDirectory>/migrate/mysql</outputDirectory>
        </fileSet>

        <fileSet>
            <directory>../broker/migrate/h2/</directory>
            <outputDirectory>/migrate/h2</outputDirectory>
        </fileSet>

        <fileSet>
            <directory>src/main/resources/</directory>
            <outputDirectory>config/</outputDirectory>
        </fileSet>

        <fileSet>
            <directory>../systemd/</directory>
            <outputDirectory>/systemd</outputDirectory>
        </fileSet>

        <fileSet>
            <directory>src/main/checker/</directory>
            <outputDirectory>checker/</outputDirectory>
        </fileSet>
    </fileSets>

    <files>
        <!-- executables scripts-->
        <file>
            <source>src/main/scripts/wildfirechat.sh</source>
            <outputDirectory>bin</outputDirectory>
            <fileMode>0755</fileMode>
        </file>

        <file>
            <source>src/main/scripts/stop.sh</source>
            <outputDirectory>bin</outputDirectory>
            <fileMode>0755</fileMode>
        </file>

        <file>
            <source>src/main/scripts/wildfirechat.bat</source>
            <outputDirectory>bin</outputDirectory>
        </file>

        <file>
            <source>src/main/files/README.txt</source>
        </file>

        <file>
            <source>../release_note.md</source>
        </file>

        <file>
            <source>../broker/target/moquette-broker-${project.version}.jar</source>
            <outputDirectory>lib/</outputDirectory>
            <destName>moquette-broker-${project.version}.jar</destName>
        </file>

        <file>
            <source>../sdk/target/sdk-${project.version}.jar</source>
            <outputDirectory>server_sdk/</outputDirectory>
            <destName>sdk-${project.version}.jar</destName>
        </file>

        <file>
            <source>../sdk/sdk_readme.txt</source>
            <outputDirectory>server_sdk/</outputDirectory>
            <destName>sdk_readme.txt</destName>
        </file>

        <file>
            <source>../common/target/common-${project.version}.jar</source>
            <outputDirectory>server_sdk/</outputDirectory>
            <destName>common-${project.version}.jar</destName>
        </file>

        <file>
            <source>../sdk/target/checker.jar</source>
            <outputDirectory>checker/</outputDirectory>
        </file>

        <file>
            <source>../docker/Dockerfile</source>
            <outputDirectory>docker/</outputDirectory>
        </file>

        <file>
            <source>../docker/README.md</source>
            <outputDirectory>docker/</outputDirectory>
        </file>
    </files>

    <dependencySets>
        <dependencySet>
            <outputDirectory>lib/</outputDirectory>
            <useProjectArtifact>true</useProjectArtifact> <!-- avoid inclusion of the artifact itself -->
            <excludes>
                <exclude>io.moquette:moquette-broker</exclude>
            </excludes>
        </dependencySet>
    </dependencySets>
</assembly>
