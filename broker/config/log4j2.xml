<?xml version="1.0" encoding="UTF-8"?>
<!--日志级别以及优先级排序: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->
<!--status="WARN" :用于设置log4j2自身内部日志的信息输出级别，默认是OFF-->
<!--monitorInterval="30"  :间隔秒数,自动检测配置文件的变更和重新配置本身-->
<configuration status="WARN" monitorInterval="60">
    <Properties>
        <!--自定义一些常量，之后使用${变量名}引用-->
        <Property name="MSG_LOG_HOME">./logs</Property>
        <property name="charset">UTF-8</property>
        <!--自定义的输出格式-->
        <property name="pattern">%d %-5p [%t] %C{2} (%F:%L) - %m%n</property>
    </Properties>
    <!--appenders:定义输出内容,输出格式,输出方式,日志保存策略等,常用其下三种标签[console,File,RollingFile]-->
    <appenders>
        <!--console :控制台输出的配置-->
        <console name="Console" target="SYSTEM_OUT">
            <!--PatternLayout :输出日志的格式,LOG4J2定义了输出代码,详见第二部分-->
            <PatternLayout pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n"/>
        </console>

        <RollingRandomAccessFile name="RollingFileInfo" immediateFlush="false"
                                 fileName="${MSG_LOG_HOME}/wildfirechat.log"
                                 filePattern="${MSG_LOG_HOME}/backup/wildfirechat.%d{yyyyMMddHHmm}.zip">
            <Filters>
                <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="${pattern}" />
            <Policies>
                <!-- TimeBasedTriggeringPolicy :时间滚动策略,默认0点小时产生新的文件,interval="6" : 自定义文件滚动时间间隔,每隔6小时产生新文件, modulate="true" : 产生文件是否以0点偏移时间,即6点,12点,18点,0点-->
                <TimeBasedTriggeringPolicy interval="6" modulate="true"/>
                <!-- SizeBasedTriggeringPolicy :文件大小滚动策略-->
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>

            <DefaultRolloverStrategy max="24">
                <Delete basePath="${MSG_LOG_HOME}" maxDepth="2">
                    <IfFileName glob="*/wildfirechat.*.zip" />
                    <IfLastModified age="24H" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="RollingFileWarn" immediateFlush="true"
                                 fileName="${MSG_LOG_HOME}/wildfirechat_warn.log"
                                 filePattern="${MSG_LOG_HOME}/backup/wildfirechat_warn.%d{yyyyMMddHH}.zip">
            <Filters>
                <ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="${pattern}" />
            <Policies>
                <!-- TimeBasedTriggeringPolicy :时间滚动策略,默认0点小时产生新的文件,interval="6" : 自定义文件滚动时间间隔,每隔6小时产生新文件, modulate="true" : 产生文件是否以0点偏移时间,即6点,12点,18点,0点-->
                <TimeBasedTriggeringPolicy interval="6" modulate="true"/>
                <!-- SizeBasedTriggeringPolicy :文件大小滚动策略-->
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>

            <DefaultRolloverStrategy max="24">
                <Delete basePath="${MSG_LOG_HOME}" maxDepth="2">
                    <IfFileName glob="*/wildfirechat_warn.*.zip" />
                    <IfLastModified age="24H" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="RollingFileError" immediateFlush="true"
                                 fileName="${MSG_LOG_HOME}/wildfirechat_error.log"
                                 filePattern="${MSG_LOG_HOME}/backup/wildfirechat_error.%d{yyyyMMddHH}.zip">
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="${pattern}" />
            <Policies>
                <!-- TimeBasedTriggeringPolicy :时间滚动策略,默认0点小时产生新的文件,interval="6" : 自定义文件滚动时间间隔,每隔6小时产生新文件, modulate="true" : 产生文件是否以0点偏移时间,即6点,12点,18点,0点-->
                <TimeBasedTriggeringPolicy interval="6" modulate="true"/>
                <!-- SizeBasedTriggeringPolicy :文件大小滚动策略-->
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>

            <DefaultRolloverStrategy max="24">
                <Delete basePath="${MSG_LOG_HOME}" maxDepth="2">
                    <IfFileName glob="*/wildfirechat_error.*.zip" />
                    <IfLastModified age="24H" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
    </appenders>

    <!--然后定义logger，只有定义了logger并引入的appender，appender才会生效-->
    <loggers>
        <!--过滤掉spring和mybatis的一些无用的DEBUG信息-->
        <!--Logger节点用来单独指定日志的形式，name为包路径,比如要为org.springframework包下所有日志指定为INFO级别等。 -->
        <!--        <logger name="org.springframework" level="INFO"></logger>-->
        <!--        <logger name="org.mybatis" level="INFO"></logger>-->
        <!-- Root节点用来指定项目的根日志，如果没有单独指定Logger，那么就会默认使用该Root日志输出 -->
        <root level="INFO">
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFileInfo"/>
            <appender-ref ref="RollingFileWarn"/>
            <appender-ref ref="RollingFileError"/>
        </root>
        <!--AsyncLogger :异步日志,LOG4J有三种日志模式,全异步日志,混合模式,同步日志,性能从高到底,线程越多效率越高,也可以避免日志卡死线程情况发生-->
        <!--additivity="false" : additivity设置事件是否在root logger输出，为了避免重复输出，可以在Logger 标签下设置additivity为”false”-->
        <AsyncLogger name="AsyncLogger" level="trace" includeLocation="true" additivity="false">
            <appender-ref ref="RollingFileInfo"/>
        </AsyncLogger>
    </loggers>
</configuration>
